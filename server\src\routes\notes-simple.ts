import express, { Request, Response } from 'express';
import { config } from '../config/database-simple';
import { protect } from '../middleware/auth';
import { Op } from 'sequelize';

const router = express.Router();

// 获取便签列表
router.get('/', protect, async (req: any, res: Response) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      category = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      isArchived = false,
      isPinned
    } = req.query;

    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 构建查询条件
    const whereCondition: any = {
      userId: req.user.id,
      isDeleted: false,
      isArchived: isArchived === 'true'
    };

    // 搜索条件
    if (search) {
      whereCondition[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { content: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 分类筛选
    if (category) {
      whereCondition.category = category;
    }

    // 置顶筛选
    if (isPinned !== undefined) {
      whereCondition.isPinned = isPinned === 'true';
    }

    // 分页计算
    const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

    // 查询便签
    const { count, rows } = await Note.findAndCountAll({
      where: whereCondition,
      order: [[sortBy as string, sortOrder as string]],
      limit: parseInt(limit as string),
      offset: offset,
    });

    return res.json({
      success: true,
      data: {
        data: rows,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: count,
          pages: Math.ceil(count / parseInt(limit as string))
        }
      }
    });
  } catch (error) {
    console.error('获取便签列表错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取单个便签
router.get('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;
    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const note = await Note.findOne({
      where: {
        id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        message: '便签不存在',
      });
    }

    return res.json({
      success: true,
      data: note,
    });
  } catch (error) {
    console.error('获取便签错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 创建便签
router.post('/', protect, async (req: any, res: Response) => {
  try {
    const { title, content, category, tags = [], color, isPinned = false } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容不能为空',
      });
    }

    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const note = await Note.create({
      title,
      content,
      category,
      tags,
      color,
      isPinned,
      userId: req.user.id,
      isArchived: false,
      isDeleted: false,
    });

    return res.status(201).json({
      success: true,
      message: '便签创建成功',
      data: note,
    });
  } catch (error) {
    console.error('创建便签错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 更新便签
router.put('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const note = await Note.findOne({
      where: {
        id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        message: '便签不存在',
      });
    }

    await note.update(updateData);

    return res.json({
      success: true,
      message: '便签更新成功',
      data: note,
    });
  } catch (error) {
    console.error('更新便签错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 删除便签
router.delete('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;

    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const note = await Note.findOne({
      where: {
        id,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        message: '便签不存在',
      });
    }

    // 软删除
    await note.update({ isDeleted: true });

    return res.json({
      success: true,
      message: '便签删除成功',
    });
  } catch (error) {
    console.error('删除便签错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取便签统计
router.get('/stats/overview', protect, async (req: any, res: Response) => {
  try {
    const Note = config.sequelize.models.Note;
    
    if (!Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const totalNotes = await Note.count({
      where: {
        userId: req.user.id,
        isDeleted: false
      }
    });

    const pinnedNotes = await Note.count({
      where: {
        userId: req.user.id,
        isDeleted: false,
        isPinned: true
      }
    });

    const archivedNotes = await Note.count({
      where: {
        userId: req.user.id,
        isDeleted: false,
        isArchived: true
      }
    });

    return res.json({
      success: true,
      data: {
        total: totalNotes,
        pinned: pinnedNotes,
        archived: archivedNotes,
        active: totalNotes - archivedNotes
      }
    });
  } catch (error) {
    console.error('获取便签统计错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

module.exports = router;
