{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "rootDir": "./src", "outDir": "./dist", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/middleware/*": ["./middleware/*"], "@/utils/*": ["./utils/*"], "@/config/*": ["./config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}