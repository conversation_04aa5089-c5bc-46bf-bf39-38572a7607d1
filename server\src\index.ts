import compression from 'compression';
import cors from 'cors';
import express from 'express';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { createServer } from 'http';
import morgan from 'morgan';
import { config } from './config/database';
// import { redisConfig } from './config/redis';
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';
// import { performanceMonitor, setupPerformanceAlerts } from './middleware/performance';
// import { setupQueueMonitoring, setupScheduledJobs } from './services/queue';
// import WebSocketService from './services/websocket';

const app = express();
const server = createServer(app);
const PORT = process.env.PORT || 5000;

// 全局变量存储WebSocket服务实例
// let wsService: WebSocketService;

// 安全中间件
app.use(helmet());
app.use(compression());

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
}));

// 请求限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 限制每个IP 100个请求
  message: {
    error: '请求过于频繁，请稍后再试',
  },
});
app.use('/api', limiter);

// 日志中间件
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// 解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 性能监控中间件
// app.use(performanceMonitor);

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API路由
import authRoutes from './routes/auth';
// import notesRoutes from './routes/notes';
// import categoriesRoutes from './routes/categories';
// import searchRoutes from './routes/search';
// import exportRoutes from './routes/export';
// import monitoringRoutes from './routes/monitoring';
// import adminRoutes from './routes/admin';

app.use('/api/auth', authRoutes);
// app.use('/api/notes', notesRoutes);
// app.use('/api/categories', categoriesRoutes);
// app.use('/api/search', searchRoutes);
// app.use('/api/export', exportRoutes);
// app.use('/api/monitoring', monitoringRoutes);
// app.use('/api/admin', adminRoutes);

// 错误处理中间件
app.use(notFound);
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    await config.connectDB();

    // 连接Redis（可选，如果Redis不可用则跳过缓存功能）
    // try {
    //   await redisConfig.connect();
    //   console.log('✅ Redis 连接成功，缓存功能已启用');
    // } catch (error) {
    //   console.warn('⚠️ Redis 连接失败，缓存功能已禁用:', (error as Error).message);
    // }

    // 初始化WebSocket服务
    // wsService = new WebSocketService(server);

    // 设置队列监控和定时任务
    // setupQueueMonitoring();
    // setupScheduledJobs();

    // 设置性能监控报警
    // setupPerformanceAlerts();

    server.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`);
      console.log(`📊 健康检查: http://localhost:${PORT}/health`);
      console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔌 WebSocket 服务已启动`);
    });
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('🔄 收到SIGTERM信号，正在关闭服务器...');
  await gracefulShutdown();
});

process.on('SIGINT', async () => {
  console.log('🔄 收到SIGINT信号，正在关闭服务器...');
  await gracefulShutdown();
});

const gracefulShutdown = async () => {
  try {
    // 关闭数据库连接
    await config.disconnectDB();

    // 关闭Redis连接
    // await redisConfig.disconnect();

    console.log('✅ 服务器已优雅关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭服务器时出错:', error);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

export default app;
