{"name": "sequelize-typescript", "version": "2.1.6", "description": "Decorators and some other features for sequelize", "scripts": {"build": "tsc", "test": "mocha test/**/*.spec.ts", "cover": "nyc mocha test/**/*.spec.ts", "lint": "eslint --ext .ts src/ test/", "lint:fix": "npm run lint -- --fix", "markdownlint": "markdownlint '**/*.md' --ignore node_modules --ignore CHANGELOG.md", "release": "release-it", "_postinstall": "husky install", "prepublishOnly": "pinst --disable && npm run build", "postpublish": "pinst --enable"}, "repository": {"type": "git", "url": "git+https://github.com/RobinBus<PERSON>mann/sequelize-typescript.git"}, "keywords": ["orm", "object relational mapper", "sequelize", "typescript", "decorators", "mysql", "sqlite", "postgresql", "postgres", "mssql"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/sequelize-typescript/issues"}, "homepage": "https://github.com/<PERSON><PERSON><PERSON><PERSON>/sequelize-typescript#readme", "main": "dist/index.js", "types": "dist/index.d.ts", "browser": "dist/browser/index.js", "engines": {"node": ">=10.0.0"}, "release-it": {"git": {"commit": false, "push": false, "tag": false}, "github": {"release": false}, "npm": {"publish": false}, "hooks": {"before:init": ["npm run lint", "npm run markdownlint", "npm run build", "npm run test"]}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "nyc": {"lines": 85, "statements": 85, "functions": 85, "branches": 85, "include": ["src"], "exclude": ["test"], "extension": [".ts"], "reporter": ["lcov", "text-summary"], "cache": true, "all": true, "check-coverage": true, "report-dir": "./coverage"}, "dependencies": {"glob": "7.2.0"}, "devDependencies": {"@commitlint/cli": "17.2.0", "@commitlint/config-conventional": "17.2.0", "@release-it/conventional-changelog": "5.1.1", "@types/chai": "4.3.4", "@types/chai-as-promised": "7.1.5", "@types/chai-datetime": "0.0.37", "@types/lodash": "4.14.190", "@types/mocha": "9.1.0", "@types/node": "18.11.9", "@types/prettyjson": "0.0.30", "@types/sinon": "10.0.11", "@types/sinon-chai": "3.2.9", "@typescript-eslint/eslint-plugin": "5.44.0", "@typescript-eslint/parser": "5.42.0", "chai": "4.3.7", "chai-as-promised": "7.1.1", "chai-datetime": "1.8.0", "codecov": "3.8.3", "copyfiles": "2.4.1", "eslint": "8.27.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-prettier": "4.2.1", "has-flag": "5.0.1", "husky": "8.0.2", "lodash": "4.17.21", "markdownlint-cli": "0.32.2", "mocha": "9.2.2", "moment": "2.29.4", "mysql2": "2.3.3", "nyc": "15.1.0", "pinst": "3.0.0", "prettier": "2.8.0", "prettyjson": "1.2.5", "reflect-metadata": "0.1.13", "release-it": "15.5.0", "sequelize": "6.29.0", "sinon": "13.0.1", "sinon-chai": "3.7.0", "source-map-support": "0.5.21", "sqlite3": "5.1.2", "ts-node": "10.9.1", "typescript": "4.8.4", "uuid-validate": "0.0.3"}, "peerDependencies": {"@types/node": "*", "@types/validator": "*", "reflect-metadata": "*", "sequelize": ">=6.20.1"}}