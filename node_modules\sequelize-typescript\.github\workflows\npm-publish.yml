# For more information see: https://help.github.com/actions/language-and-framework-guides/publishing-nodejs-packages

name: 'Node.js Package'

on:
  release:
    types: [created]

jobs:
  publish-npm:
    runs-on: 'ubuntu-latest'
    steps:
      - uses: 'actions/checkout@v3.1.0'

      - uses: 'actions/setup-node@v3.5.1'
        with:
          node-version: 16
          registry-url: 'https://registry.npmjs.org/'

      - run: 'npm ci'
      - run: 'npm run build'
      - run: 'npm publish'
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
