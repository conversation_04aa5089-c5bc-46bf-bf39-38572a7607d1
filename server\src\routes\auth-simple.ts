import bcrypt from 'bcryptjs';
import express, { Request, Response } from 'express';
import { config } from '../config/database-simple';
import { protect } from '../middleware/auth';
import { JWTUtils } from '../utils/jwt-simple';

const router = express.Router();

// 注册
router.post('/register', async (req: Request, res: Response) => {
  try {
    const { email, password, name } = req.body;

    // 验证必填字段
    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱、密码和姓名',
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: '邮箱格式不正确',
      });
    }

    // 验证密码长度
    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码长度至少6位',
      });
    }

    const User = config.sequelize.models.User;

    if (!User) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 检查用户是否已存在
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册',
      });
    }

    // 加密密码
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 创建用户
    const user = await User.create({
      email,
      password: hashedPassword,
      name,
      role: 'user',
      isActive: true,
    });

    // 生成令牌
    const payload = {
      id: (user as any).id,
      email: (user as any).email,
      role: (user as any).role,
    };

    const accessToken = JWTUtils.generateAccessToken(payload);
    const refreshToken = JWTUtils.generateRefreshToken(payload);

    // 保存refresh token到数据库
    await user.update({ refreshToken });

    return res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: {
          id: (user as any).id,
          email: (user as any).email,
          name: (user as any).name,
          role: (user as any).role,
          avatar: (user as any).avatar,
          createdAt: (user as any).createdAt,
        },
        accessToken,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('注册错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 登录
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    // 验证必填字段
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供邮箱和密码',
      });
    }

    const User = config.sequelize.models.User;

    if (!User) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 查找用户
    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误',
      });
    }

    // 检查用户是否激活
    if (!(user as any).isActive) {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用',
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, (user as any).password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误',
      });
    }

    // 生成令牌
    const payload = {
      id: (user as any).id,
      email: (user as any).email,
      role: (user as any).role,
    };

    const accessToken = JWTUtils.generateAccessToken(payload);
    const refreshToken = JWTUtils.generateRefreshToken(payload);

    // 更新最后登录时间和refresh token
    await user.update({
      lastLoginAt: new Date(),
      refreshToken,
    });

    return res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: (user as any).id,
          email: (user as any).email,
          name: (user as any).name,
          role: (user as any).role,
          avatar: (user as any).avatar,
          lastLoginAt: (user as any).lastLoginAt,
        },
        accessToken,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('登录错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 刷新令牌
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json({
        success: false,
        message: '未提供刷新令牌',
      });
    }

    try {
      // 验证refresh token
      const decoded = JWTUtils.verifyRefreshToken(refreshToken);

      const User = config.sequelize.models.User;

      if (!User) {
        return res.status(500).json({
          success: false,
          message: '数据库模型未初始化',
        });
      }

      const user = await User.findByPk(decoded.id);

      if (!user || (user as any).refreshToken !== refreshToken) {
        return res.status(401).json({
          success: false,
          message: '无效的刷新令牌',
        });
      }

      // 生成新的令牌
      const payload = {
        id: (user as any).id,
        email: (user as any).email,
        role: (user as any).role,
      };

      const newAccessToken = JWTUtils.generateAccessToken(payload);
      const newRefreshToken = JWTUtils.generateRefreshToken(payload);

      // 更新refresh token
      await user.update({ refreshToken: newRefreshToken });

      return res.json({
        success: true,
        message: '令牌刷新成功',
        data: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        },
      });
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: '无效的刷新令牌',
      });
    }
  } catch (error) {
    console.error('刷新令牌错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取当前用户信息
router.get('/me', protect, async (req: any, res: Response) => {
  try {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          email: req.user.email,
          name: req.user.name,
          role: req.user.role,
          avatar: req.user.avatar,
          isActive: req.user.isActive,
          lastLoginAt: req.user.lastLoginAt,
          createdAt: req.user.createdAt,
          updatedAt: req.user.updatedAt,
        },
      },
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 更新用户资料
router.put('/profile', protect, async (req: any, res: Response) => {
  try {
    const { name, avatar } = req.body;
    const User = config.sequelize.models.User;

    if (!User) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 验证输入
    if (!name || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: '姓名不能为空',
      });
    }

    if (name.trim().length > 50) {
      return res.status(400).json({
        success: false,
        message: '姓名长度不能超过50个字符',
      });
    }

    // 更新用户信息
    const updateData: any = {
      name: name.trim(),
    };

    if (avatar !== undefined) {
      updateData.avatar = avatar;
    }

    await User.update(updateData, {
      where: { id: req.user.id }
    });

    // 获取更新后的用户信息
    const updatedUser = await User.findByPk(req.user.id, {
      attributes: ['id', 'email', 'name', 'avatar', 'role', 'createdAt']
    });

    return res.json({
      success: true,
      message: '资料更新成功',
      data: {
        user: updatedUser
      }
    });
  } catch (error) {
    console.error('更新用户资料错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 修改密码
router.put('/password', protect, async (req: any, res: Response) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const User = config.sequelize.models.User;

    if (!User) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 验证输入
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '当前密码和新密码都不能为空',
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: '新密码长度至少6位',
      });
    }

    // 获取用户信息
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
      });
    }

    // 验证当前密码
    const bcrypt = require('bcryptjs');
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, (user as any).password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '当前密码错误',
      });
    }

    // 加密新密码
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await User.update(
      { password: hashedNewPassword },
      { where: { id: req.user.id } }
    );

    return res.json({
      success: true,
      message: '密码修改成功',
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 登出
router.post('/logout', protect, async (req: any, res: Response) => {
  try {
    // 清除refresh token
    await req.user.update({ refreshToken: null });

    res.json({
      success: true,
      message: '登出成功',
    });
  } catch (error) {
    console.error('登出错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

module.exports = router;
