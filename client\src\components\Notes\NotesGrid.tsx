import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { FileText, Plus } from 'lucide-react';
import React from 'react';
import { NoteCard } from './NoteCard';

interface Note {
  _id: string;
  title: string;
  content: string;
  category?: string;
  tags: string[];
  color: string;
  isPinned: boolean;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
}

interface NotesGridProps {
  notes: Note[];
  isLoading: boolean;
  onEdit: (note: Note) => void;
  onDelete: (id: string) => void;
  onPin: (id: string, isPinned: boolean) => void;
  onArchive: (id: string, isArchived: boolean) => void;
  onCreateNote?: () => void;
}

export const NotesGrid: React.FC<NotesGridProps> = ({
  notes,
  isLoading,
  onEdit,
  onDelete,
  onPin,
  onArchive,
  onCreateNote,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  if (notes.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <FileText className="w-12 h-12 text-gray-400" />
          </div>

          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            还没有便签
          </h3>

          <p className="text-gray-600 mb-6">
            开始创建您的第一个便签，记录重要的想法和信息。
          </p>

          {onCreateNote && (
            <Button onClick={onCreateNote} size="lg">
              <Plus className="w-4 h-4 mr-2" />
              创建便签
            </Button>
          )}
        </div>
      </div>
    );
  }

  // 分离置顶和普通便签
  const pinnedNotes = notes.filter(note => note && note.isPinned);
  const regularNotes = notes.filter(note => note && !note.isPinned);

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-6">
        {/* 置顶便签区域 */}
        {pinnedNotes.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                📌 置顶便签
              </h2>
              <div className="ml-2 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                {pinnedNotes.length}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {pinnedNotes.map((note) => (
                <NoteCard
                  key={note.id}
                  note={note}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onPin={onPin}
                  onArchive={onArchive}
                />
              ))}
            </div>
          </div>
        )}

        {/* 普通便签区域 */}
        {regularNotes.length > 0 && (
          <div>
            {pinnedNotes.length > 0 && (
              <div className="flex items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  📝 所有便签
                </h2>
                <div className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  {regularNotes.length}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {regularNotes.map((note) => (
                <NoteCard
                  key={note.id}
                  note={note}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onPin={onPin}
                  onArchive={onArchive}
                />
              ))}
            </div>
          </div>
        )}

        {/* 底部间距 */}
        <div className="h-20" />
      </div>
    </div>
  );
};
