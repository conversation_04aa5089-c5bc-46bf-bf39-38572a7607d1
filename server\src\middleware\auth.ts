import { NextFunction, Request, Response } from 'express';
import { config } from '../config/database-simple';
import { JWTUtils } from '../utils/jwt-simple';

interface AuthRequest extends Request {
  user?: any;
}

export const protect = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    let token;

    // 从Authorization header获取token
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      res.status(401).json({
        success: false,
        message: '未提供访问令牌',
      });
      return;
    }

    try {
      // 验证token
      const decoded = JWTUtils.verifyAccessToken(token);

      // 从数据库获取用户信息
      const User = config.sequelize.models.User;
      const user = await User?.findByPk(decoded.id);

      if (!user) {
        res.status(401).json({
          success: false,
          message: '用户不存在',
        });
        return;
      }

      // 检查用户是否激活
      if (!(user as any).isActive) {
        res.status(401).json({
          success: false,
          message: '账户已被禁用',
        });
        return;
      }

      req.user = user;
      next();
    } catch (error) {
      res.status(401).json({
        success: false,
        message: '无效的访问令牌',
      });
      return;
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '服务器错误',
    });
    return;
  }
};

export const restrictTo = (...roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        message: '权限不足',
      });
      return;
    }
    next();
  };
};
