import express from 'express';

const router = express.Router();

// 健康检查
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'LoftNotes API 服务正常运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    database: 'PostgreSQL',
  });
});

// 数据库连接状态检查
router.get('/db', async (req, res) => {
  try {
    const { config } = require('../config/database-simple');
    await config.sequelize.authenticate();

    res.json({
      success: true,
      message: '数据库连接正常',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '数据库连接失败',
      error: (error as Error).message,
      timestamp: new Date().toISOString(),
    });
  }
});

module.exports = router;
