<!-- Before submitting an issue, please consult our docs (https://github.com/<PERSON><PERSON><PERSON><PERSON>mann/sequelize-typescript) and check whether your problem was already reported (https://github.com/RobinBuschmann/sequelize-typescript/issues) -->

<!-- Please make sure you are posting an issue pertaining to sequelize-typescript. If you are having an issue with sequelize itself, please consult the sequelize team (https://github.com/sequelize/sequelize/issues) -->

# Issue

## Versions

<!-- Please let us know which sequelize, sequelize-typescript and typescript version are you using -->

- sequelize:
- sequelize-typescript:
- typescript:

## Issue type

<!--  (check one with "x") -->

- [ ] bug report
- [ ] feature request

## Actual behavior

<!-- Describe how the bug manifests. -->

## Expected behavior

<!-- Describe what the behavior would be without the bug. -->

## Steps to reproduce

<!--  Please explain the steps required to duplicate the issue, especially if you are able to provide a sample application. -->

## Related code

<!-- If you are able to illustrate the bug or feature request with an example, please provide a sample application via one of the following means:

- A sample application via GitHub (Best option, since its much easier for us to investigate, so that we can come back to you more recently)
- A code snippet below (Please make sure, that the snippet at least includes tsconfig and the sequelize options)

-->

```ts
insert short code snippets here
```
