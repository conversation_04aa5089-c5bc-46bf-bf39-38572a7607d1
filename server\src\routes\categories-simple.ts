import express, { Request, Response } from 'express';
import { config } from '../config/database-simple';
import { protect } from '../middleware/auth';

const router = express.Router();

// 获取分类列表
router.get('/', protect, async (req: any, res: Response) => {
  try {
    const Category = config.sequelize.models.Category;
    
    if (!Category) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const categories = await Category.findAll({
      where: {
        userId: req.user.id
      },
      order: [['sortOrder', 'ASC'], ['createdAt', 'ASC']]
    });

    return res.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('获取分类列表错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取单个分类
router.get('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;
    const Category = config.sequelize.models.Category;
    
    if (!Category) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const category = await Category.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在',
      });
    }

    return res.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('获取分类错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 创建分类
router.post('/', protect, async (req: any, res: Response) => {
  try {
    const { name, description, color = '#3B82F6', icon } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '分类名称不能为空',
      });
    }

    const Category = config.sequelize.models.Category;
    
    if (!Category) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    // 检查分类名称是否已存在
    const existingCategory = await Category.findOne({
      where: {
        name,
        userId: req.user.id
      }
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: '分类名称已存在',
      });
    }

    // 获取当前用户的分类数量，用于设置排序
    const categoryCount = await Category.count({
      where: {
        userId: req.user.id
      }
    });

    const category = await Category.create({
      name,
      description,
      color,
      icon,
      sortOrder: categoryCount,
      userId: req.user.id,
    });

    return res.status(201).json({
      success: true,
      message: '分类创建成功',
      data: category,
    });
  } catch (error) {
    console.error('创建分类错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 更新分类
router.put('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;
    const { name, description, color, icon } = req.body;

    const Category = config.sequelize.models.Category;
    
    if (!Category) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const category = await Category.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在',
      });
    }

    // 如果更新名称，检查是否与其他分类重名
    if (name && name !== (category as any).name) {
      const existingCategory = await Category.findOne({
        where: {
          name,
          userId: req.user.id,
          id: { [require('sequelize').Op.ne]: id }
        }
      });

      if (existingCategory) {
        return res.status(400).json({
          success: false,
          message: '分类名称已存在',
        });
      }
    }

    const updateData: any = {};
    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (color) updateData.color = color;
    if (icon !== undefined) updateData.icon = icon;

    await category.update(updateData);

    return res.json({
      success: true,
      message: '分类更新成功',
      data: category,
    });
  } catch (error) {
    console.error('更新分类错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 删除分类
router.delete('/:id', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;

    const Category = config.sequelize.models.Category;
    const Note = config.sequelize.models.Note;
    
    if (!Category || !Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const category = await Category.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在',
      });
    }

    // 检查是否有便签使用此分类
    const notesCount = await Note.count({
      where: {
        category: (category as any).name,
        userId: req.user.id,
        isDeleted: false
      }
    });

    if (notesCount > 0) {
      return res.status(400).json({
        success: false,
        message: `无法删除分类，还有 ${notesCount} 个便签使用此分类`,
      });
    }

    await category.destroy();

    return res.json({
      success: true,
      message: '分类删除成功',
    });
  } catch (error) {
    console.error('删除分类错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

// 获取分类统计
router.get('/:id/stats', protect, async (req: any, res: Response) => {
  try {
    const { id } = req.params;

    const Category = config.sequelize.models.Category;
    const Note = config.sequelize.models.Note;
    
    if (!Category || !Note) {
      return res.status(500).json({
        success: false,
        message: '数据库模型未初始化',
      });
    }

    const category = await Category.findOne({
      where: {
        id,
        userId: req.user.id
      }
    });

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在',
      });
    }

    const totalNotes = await Note.count({
      where: {
        category: (category as any).name,
        userId: req.user.id,
        isDeleted: false
      }
    });

    const archivedNotes = await Note.count({
      where: {
        category: (category as any).name,
        userId: req.user.id,
        isDeleted: false,
        isArchived: true
      }
    });

    return res.json({
      success: true,
      data: {
        total: totalNotes,
        active: totalNotes - archivedNotes,
        archived: archivedNotes
      }
    });
  } catch (error) {
    console.error('获取分类统计错误:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
    });
  }
});

module.exports = router;
