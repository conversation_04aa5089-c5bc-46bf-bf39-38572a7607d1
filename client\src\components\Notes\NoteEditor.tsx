import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useNotesStore } from '@/store/notesStore';
import { Archive, Palette, Pin, Save, Tag, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';

interface Note {
  _id?: string;
  title: string;
  content: string;
  category?: string;
  tags: string[];
  color: string;
  isPinned: boolean;
  isArchived: boolean;
}

interface NoteEditorProps {
  note?: Note | null;
  isOpen: boolean;
  onClose: () => void;
}

const colorOptions = [
  { name: '默认', value: '#ffffff' },
  { name: '黄色', value: '#fef3c7' },
  { name: '绿色', value: '#d1fae5' },
  { name: '蓝色', value: '#dbeafe' },
  { name: '紫色', value: '#e9d5ff' },
  { name: '粉色', value: '#fce7f3' },
  { name: '橙色', value: '#fed7aa' },
  { name: '红色', value: '#fee2e2' },
];

export const NoteEditor: React.FC<NoteEditorProps> = ({ note, isOpen, onClose }) => {
  const { categories, createNote, updateNote, fetchCategories } = useNotesStore();
  const [formData, setFormData] = useState<Partial<Note>>({
    title: '',
    content: '',
    category: '',
    tags: [],
    color: '#ffffff',
    isPinned: false,
    isArchived: false,
  });
  const [tagInput, setTagInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchCategories();
      if (note) {
        setFormData(note);
      } else {
        setFormData({
          title: '',
          content: '',
          category: '',
          tags: [],
          color: '#ffffff',
          isPinned: false,
          isArchived: false,
        });
      }
    }
  }, [note, isOpen, fetchCategories]);

  const handleSave = async () => {
    if (!formData.title.trim()) {
      toast.error('请输入便签标题');
      return;
    }

    if (!formData.content.trim()) {
      toast.error('请输入便签内容');
      return;
    }

    setIsLoading(true);
    try {
      if (note?.id) {
        await updateNote(note.id, formData);
        toast.success('便签更新成功');
      } else {
        await createNote(formData);
        toast.success('便签创建成功');
      }
      onClose();
    } catch (error) {
      toast.error('保存失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddTag = () => {
    const tag = tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {note ? '编辑便签' : '新建便签'}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-auto space-y-4">
          {/* 标题 */}
          <div>
            <Input
              placeholder="输入便签标题..."
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="text-lg font-semibold"
            />
          </div>

          {/* 工具栏 */}
          <div className="flex flex-wrap items-center gap-2 p-2 bg-muted/50 rounded-lg">
            {/* 分类选择 */}
            <Select
              value={formData.category || "__none__"}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                category: value === "__none__" ? "" : value
              }))}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="选择分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__none__">无分类</SelectItem>
                {categories?.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      {category.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* 颜色选择 */}
            <div className="flex items-center gap-1">
              <Palette className="h-4 w-4 text-muted-foreground" />
              <div className="flex gap-1">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    className={`w-6 h-6 rounded-full border-2 ${formData.color === color.value ? 'border-primary' : 'border-gray-300'
                      }`}
                    style={{ backgroundColor: color.value }}
                    onClick={() => setFormData(prev => ({ ...prev, color: color.value }))}
                    title={color.name}
                  />
                ))}
              </div>
            </div>

            {/* 置顶和归档 */}
            <Button
              variant={formData.isPinned ? "default" : "outline"}
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, isPinned: !prev.isPinned }))}
            >
              <Pin className="h-4 w-4 mr-1" />
              置顶
            </Button>

            <Button
              variant={formData.isArchived ? "default" : "outline"}
              size="sm"
              onClick={() => setFormData(prev => ({ ...prev, isArchived: !prev.isArchived }))}
            >
              <Archive className="h-4 w-4 mr-1" />
              归档
            </Button>
          </div>

          {/* 内容 */}
          <div>
            <Textarea
              placeholder="开始写下你的想法..."
              value={formData.content}
              onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
              className="min-h-[300px] resize-none"
              style={{ backgroundColor: formData.color !== '#ffffff' ? formData.color : undefined }}
            />
          </div>

          {/* 标签 */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Tag className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="添加标签..."
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button size="sm" onClick={handleAddTag}>
                添加
              </Button>
            </div>

            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {formData.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="cursor-pointer">
                    #{tag}
                    <X
                      className="h-3 w-3 ml-1"
                      onClick={() => handleRemoveTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 底部操作栏 */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="text-sm text-muted-foreground">
            {formData.content.length} 字符
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              取消
            </Button>
            <Button onClick={handleSave} disabled={isLoading}>
              <Save className="h-4 w-4 mr-1" />
              {isLoading ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
