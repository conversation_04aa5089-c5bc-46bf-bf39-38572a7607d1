# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: 'Node.js CI'

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: 'ubuntu-latest'

    strategy:
      matrix:
        node-version: [10.x, 12.x, 14.x, 16.x]

    steps:
      - uses: 'actions/checkout@v3.1.0'

      - name: Use Node.js ${{ matrix.node-version }}
        uses: 'actions/setup-node@v3.5.1'
        with:
          node-version: ${{ matrix.node-version }}

      - run: 'npm ci'
      - name: Lint
        if: ${{ matrix.node-version != '10.x' }}
        run: 'npm run lint'
      - name: Markdownlint
        if: ${{ matrix.node-version != '10.x' }}
        run: 'npm run markdownlint'
      - run: 'npm run build'
      - run: 'DISABLE_LOGGING=1 npm run cover'
      - run: 'npx nyc report --reporter=lcov > coverage.lcov && npx codecov'
