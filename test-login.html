<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoftNotes API 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 LoftNotes API 测试</h1>
        
        <div class="form-group">
            <label for="email">邮箱:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="demo123456">
        </div>
        
        <div class="form-group">
            <label for="name">姓名 (仅注册时需要):</label>
            <input type="text" id="name" value="Demo User">
        </div>
        
        <button onclick="testRegister()">测试注册</button>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testHealth()">健康检查</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isError ? 'error' : 'success'}`;
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        async function testHealth() {
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                showResult(data);
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }
        
        async function testRegister() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const name = document.getElementById('name').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password, name })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(data);
                    // 保存令牌
                    if (data.data && data.data.accessToken) {
                        localStorage.setItem('token', data.data.accessToken);
                        localStorage.setItem('refreshToken', data.data.refreshToken);
                    }
                } else {
                    showResult(data, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(data);
                    // 保存令牌
                    if (data.data && data.data.accessToken) {
                        localStorage.setItem('token', data.data.accessToken);
                        localStorage.setItem('refreshToken', data.data.refreshToken);
                    }
                } else {
                    showResult(data, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }
        
        // 页面加载时测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
