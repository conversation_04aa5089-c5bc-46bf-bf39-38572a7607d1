# LoftNotes API 测试脚本

Write-Host "🚀 开始测试 LoftNotes API..." -ForegroundColor Green

$baseUrl = "http://localhost:5000"

# 1. 测试健康检查
Write-Host "`n📊 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/health" -Method GET
    Write-Host "✅ 健康检查成功: $($healthResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试用户注册
Write-Host "`n👤 测试用户注册..." -ForegroundColor Yellow
$registerData = @{
    email = "<EMAIL>"
    password = "demo123456"
    name = "Demo User"
} | ConvertTo-Json

try {
    $registerResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/register" -Method POST -Body $registerData -ContentType "application/json"
    Write-Host "✅ 用户注册成功: $($registerResponse.data.user.name)" -ForegroundColor Green
    $accessToken = $registerResponse.data.accessToken
    Write-Host "🔑 获得访问令牌: $($accessToken.Substring(0, 20))..." -ForegroundColor Cyan
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "⚠️ 用户可能已存在，尝试登录..." -ForegroundColor Yellow
        
        # 3. 测试用户登录
        Write-Host "`n🔐 测试用户登录..." -ForegroundColor Yellow
        $loginData = @{
            email = "<EMAIL>"
            password = "demo123456"
        } | ConvertTo-Json
        
        try {
            $loginResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/login" -Method POST -Body $loginData -ContentType "application/json"
            Write-Host "✅ 用户登录成功: $($loginResponse.data.user.name)" -ForegroundColor Green
            $accessToken = $loginResponse.data.accessToken
            Write-Host "🔑 获得访问令牌: $($accessToken.Substring(0, 20))..." -ForegroundColor Cyan
        } catch {
            Write-Host "❌ 用户登录失败: $($_.Exception.Message)" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ 用户注册失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# 4. 测试获取用户信息
Write-Host "`n👤 测试获取用户信息..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $accessToken"
        "Content-Type" = "application/json"
    }
    $userResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth/me" -Method GET -Headers $headers
    Write-Host "✅ 获取用户信息成功: $($userResponse.data.user.email)" -ForegroundColor Green
} catch {
    Write-Host "❌ 获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试数据库连接
Write-Host "`n🗄️ 测试数据库连接..." -ForegroundColor Yellow
try {
    $dbResponse = Invoke-RestMethod -Uri "$baseUrl/health/db" -Method GET
    Write-Host "✅ 数据库连接正常: $($dbResponse.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ 数据库连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API 测试完成！" -ForegroundColor Green
Write-Host "📝 接下来可以开发便签管理功能..." -ForegroundColor Cyan

# 显示API端点总结
Write-Host "`n📋 可用的API端点:" -ForegroundColor Blue
Write-Host "  GET  /health           - 健康检查" -ForegroundColor White
Write-Host "  GET  /health/db        - 数据库状态" -ForegroundColor White
Write-Host "  POST /api/auth/register - 用户注册" -ForegroundColor White
Write-Host "  POST /api/auth/login    - 用户登录" -ForegroundColor White
Write-Host "  GET  /api/auth/me       - 获取用户信息" -ForegroundColor White
Write-Host "  POST /api/auth/refresh  - 刷新令牌" -ForegroundColor White
Write-Host "  POST /api/auth/logout   - 用户登出" -ForegroundColor White

Write-Host "`n🌐 前端应用: http://localhost:3000" -ForegroundColor Magenta
Write-Host "🔧 后端API: http://localhost:5000" -ForegroundColor Magenta
