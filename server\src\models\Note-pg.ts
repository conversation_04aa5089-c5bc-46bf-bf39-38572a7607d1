import { Table, Column, Model, DataType, CreatedAt, UpdatedAt, ForeignKey, BelongsTo } from 'sequelize-typescript';
import { User } from './User-pg';

@Table({
  tableName: 'notes',
  timestamps: true,
})
export class Note extends Model {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
  })
  id!: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  title!: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
  })
  content!: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  category?: string;

  @Column({
    type: DataType.ARRAY(DataType.STRING),
    defaultValue: [],
  })
  tags!: string[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isPinned!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isArchived!: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  isDeleted!: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  color?: string;

  @Column({
    type: DataType.JSON,
    allowNull: true,
  })
  metadata?: any;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId!: string;

  @BelongsTo(() => User)
  user!: User;

  @CreatedAt
  createdAt!: Date;

  @UpdatedAt
  updatedAt!: Date;

  // 实例方法
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}
