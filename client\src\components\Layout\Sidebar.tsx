import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useNotesStore } from '@/store/notesStore';
import {
  Archive,
  ChevronLeft,
  ChevronRight,
  Folder,
  Settings,
  Shield,
  StickyNote,
  Tag
} from 'lucide-react';
import React from 'react';

interface SidebarProps {
  currentView: string;
  onViewChange: (view: string) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  showAdminPanel?: boolean;
}

export const Sidebar: React.FC<SidebarProps> = ({
  currentView,
  onViewChange,
  collapsed,
  onToggleCollapse,
  showAdminPanel = false,
}) => {
  const { categories, filters, setFilters } = useNotesStore();

  const menuItems = [
    {
      id: 'notes',
      label: '所有便签',
      icon: StickyNote,
      count: null,
    },
    {
      id: 'archived',
      label: '归档',
      icon: Archive,
      count: null,
    },
    ...(showAdminPanel ? [{
      id: 'admin',
      label: '管理面板',
      icon: Shield,
      count: null,
    }] : []),
    {
      id: 'settings',
      label: '设置',
      icon: Settings,
      count: null,
    },
  ];

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value });
    if (currentView !== 'notes') {
      onViewChange('notes');
    }
  };

  const handleCategoryClick = (categoryName: string) => {
    handleFilterChange('category', categoryName === filters.category ? '' : categoryName);
  };

  return (
    <div className={cn(
      "bg-white border-r border-gray-200 flex flex-col transition-all duration-300",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* 折叠按钮 */}
      <div className="p-4 border-b border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="w-full justify-center"
        >
          {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* 主菜单 */}
      <div className="flex-1 overflow-y-auto">
        <nav className="p-2">
          <div className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentView === item.id;

              return (
                <Button
                  key={item.id}
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    collapsed && "justify-center px-2"
                  )}
                  onClick={() => onViewChange(item.id)}
                >
                  <Icon className={cn("h-4 w-4", !collapsed && "mr-2")} />
                  {!collapsed && (
                    <>
                      <span className="flex-1 text-left">{item.label}</span>
                      {item.count && (
                        <Badge variant="secondary" className="ml-auto">
                          {item.count}
                        </Badge>
                      )}
                    </>
                  )}
                </Button>
              );
            })}
          </div>
        </nav>

        {/* 分类列表 */}
        {!collapsed && currentView === 'notes' && (
          <div className="p-2 border-t border-gray-200 mt-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-700 flex items-center">
                <Folder className="h-4 w-4 mr-1" />
                分类
              </h3>
            </div>

            <div className="space-y-1">
              <Button
                variant={filters.category === '' ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start text-xs"
                onClick={() => handleCategoryClick('')}
              >
                <div className="w-3 h-3 rounded-full bg-gray-400 mr-2" />
                全部
              </Button>

              {categories?.map((category) => (
                <Button
                  key={category.id}
                  variant={filters.category === category.name ? "secondary" : "ghost"}
                  size="sm"
                  className="w-full justify-start text-xs"
                  onClick={() => handleCategoryClick(category.name)}
                >
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{ backgroundColor: category.color }}
                  />
                  <span className="flex-1 text-left truncate">{category.name}</span>
                  {category.notesCount && (
                    <Badge variant="outline" className="ml-auto text-xs">
                      {category.notesCount}
                    </Badge>
                  )}
                </Button>
              )) || []}
            </div>
          </div>
        )}

        {/* 快速过滤 */}
        {!collapsed && currentView === 'notes' && (
          <div className="p-2 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-gray-700 flex items-center">
                <Tag className="h-4 w-4 mr-1" />
                快速过滤
              </h3>
            </div>

            <div className="space-y-1">
              <Button
                variant={filters.isPinned === true ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start text-xs"
                onClick={() => handleFilterChange('isPinned',
                  filters.isPinned === true ? undefined : true
                )}
              >
                📌 置顶便签
              </Button>

              <Button
                variant={filters.isArchived === true ? "secondary" : "ghost"}
                size="sm"
                className="w-full justify-start text-xs"
                onClick={() => {
                  handleFilterChange('isArchived', !filters.isArchived);
                  onViewChange('archived');
                }}
              >
                📦 归档便签
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* 底部信息 */}
      {!collapsed && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-xs text-gray-500 text-center">
            LoftNotes v1.0.0
          </div>
        </div>
      )}
    </div>
  );
};
