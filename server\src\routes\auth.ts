import express from 'express';
import { validationResult } from 'express-validator';
import { protect } from '../middleware/auth';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { User } from '../models/User';
import { JWTUtils } from '../utils/jwt';
import { userValidation } from '../utils/validation';

const router = express.Router();

// 注册
router.post('/register', userValidation.register, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0]?.msg || '验证失败', 400);
  }

  const { username, email, password } = req.body;

  // 检查用户是否已存在
  const existingUser = await User.findOne({
    $or: [{ email }, { username }]
  });

  if (existingUser) {
    throw new CustomError('用户名或邮箱已存在', 400);
  }

  // 创建用户
  const user = await User.create({
    username,
    email,
    password,
  });

  // 生成令牌
  const tokenPayload = {
    id: user._id?.toString() || '',
    email: user.email,
    role: user.role,
  };

  const { accessToken, refreshToken } = JWTUtils.generateTokenPair(tokenPayload);

  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      token: accessToken,
      refreshToken,
    },
  });
}));

// 登录
router.post('/login', userValidation.login, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0]?.msg || '验证失败', 400);
  }

  const { email, password } = req.body;

  // 查找用户（包含密码字段）
  const user = await User.findOne({ email }).select('+password');

  if (!user || !(await user.comparePassword(password))) {
    throw new CustomError('邮箱或密码错误', 401);
  }

  if (!user.isActive) {
    throw new CustomError('账户已被禁用', 401);
  }

  // 更新最后登录时间
  user.lastLoginAt = new Date();
  await user.save();

  // 生成令牌
  const tokenPayload = {
    id: user._id?.toString() || '',
    email: user.email,
    role: user.role,
  };

  const { accessToken, refreshToken } = JWTUtils.generateTokenPair(tokenPayload);

  res.json({
    success: true,
    message: '登录成功',
    data: {
      user: {
        _id: user._id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      token: accessToken,
      refreshToken,
    },
  });
}));

// 刷新令牌
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new CustomError('刷新令牌不能为空', 400);
  }

  try {
    const decoded = JWTUtils.verifyRefreshToken(refreshToken);

    // 验证用户是否仍然存在且活跃
    const user = await User.findById(decoded.id);
    if (!user || !user.isActive) {
      throw new CustomError('用户不存在或已被禁用', 401);
    }

    // 生成新的令牌对
    const tokenPayload = {
      id: user._id?.toString() || '',
      email: user.email,
      role: user.role,
    };

    const tokens = JWTUtils.generateTokenPair(tokenPayload);

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: tokens,
    });
  } catch (error) {
    throw new CustomError('刷新令牌无效', 401);
  }
}));

// 获取当前用户信息
router.get('/me', protect, asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id).populate('notesCount');

  res.json({
    success: true,
    data: { user },
  });
}));

// 更新用户资料
router.put('/profile', protect, userValidation.updateProfile, asyncHandler(async (req, res) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new CustomError(errors.array()[0]?.msg || '验证失败', 400);
  }

  const { username, avatar } = req.body;
  const userId = req.user._id;

  // 检查用户名是否已被其他用户使用
  if (username) {
    const existingUser = await User.findOne({
      username,
      _id: { $ne: userId }
    });

    if (existingUser) {
      throw new CustomError('用户名已被使用', 400);
    }
  }

  // 更新用户信息
  const user = await User.findByIdAndUpdate(
    userId,
    { ...(username && { username }), ...(avatar && { avatar }) },
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    message: '资料更新成功',
    data: { user },
  });
}));

// 修改密码
router.put('/password', protect, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    throw new CustomError('当前密码和新密码不能为空', 400);
  }

  if (newPassword.length < 6) {
    throw new CustomError('新密码至少6个字符', 400);
  }

  // 获取用户（包含密码）
  const user = await User.findById(req.user._id).select('+password');

  if (!user || !(await user.comparePassword(currentPassword))) {
    throw new CustomError('当前密码错误', 400);
  }

  // 更新密码
  user.password = newPassword;
  await user.save();

  res.json({
    success: true,
    message: '密码修改成功',
  });
}));

export default router;
