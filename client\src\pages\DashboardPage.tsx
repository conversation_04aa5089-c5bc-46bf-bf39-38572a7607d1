import { AdminPanel } from '@/components/Admin/AdminPanel';
import { Header } from '@/components/Layout/Header';
import { Sidebar } from '@/components/Layout/Sidebar';
import { NoteEditor } from '@/components/Notes/NoteEditor';
import { NotesGrid } from '@/components/Notes/NotesGrid';
import { useAuthStore } from '@/store/authStore';
import { useNotesStore } from '@/store/notesStore';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';

type ViewMode = 'notes' | 'admin';

export const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const {
    notes,
    fetchNotes,
    fetchCategories,
    updateNote,
    deleteNote,
    isLoading
  } = useNotesStore();

  const [currentView, setCurrentView] = useState<ViewMode>('notes');
  const [selectedNote, setSelectedNote] = useState<any>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  useEffect(() => {
    // 初始化数据
    fetchNotes();
    fetchCategories();
  }, [fetchNotes, fetchCategories]);

  const handleCreateNote = () => {
    setSelectedNote(null);
    setIsEditorOpen(true);
  };

  const handleEditNote = (note: any) => {
    setSelectedNote(note);
    setIsEditorOpen(true);
  };

  const handleDeleteNote = async (id: string) => {
    if (window.confirm('确定要删除这个便签吗？')) {
      try {
        await deleteNote(id);
        toast.success('便签删除成功');
      } catch (error) {
        toast.error('删除失败，请重试');
      }
    }
  };

  const handlePinNote = async (id: string, isPinned: boolean) => {
    try {
      await updateNote(id, { isPinned });
      toast.success(isPinned ? '便签已置顶' : '已取消置顶');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleArchiveNote = async (id: string, isArchived: boolean) => {
    try {
      await updateNote(id, { isArchived });
      toast.success(isArchived ? '便签已归档' : '已取消归档');
    } catch (error) {
      toast.error('操作失败，请重试');
    }
  };

  const handleCloseEditor = () => {
    setIsEditorOpen(false);
    setSelectedNote(null);
    // 重新获取便签列表以显示最新数据
    fetchNotes();
  };

  const handleOpenSettings = () => {
    navigate('/profile');
  };

  const renderMainContent = () => {
    switch (currentView) {
      case 'admin':
        return user?.role === 'admin' ? (
          <AdminPanel />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">访问被拒绝</h2>
              <p className="text-gray-600">您没有管理员权限</p>
            </div>
          </div>
        );

      default:
        return (
          <div className="flex-1 overflow-hidden">
            <NotesGrid
              notes={notes}
              isLoading={isLoading}
              onEdit={handleEditNote}
              onDelete={handleDeleteNote}
              onPin={handlePinNote}
              onArchive={handleArchiveNote}
            />
          </div>
        );
    }
  };

  return (
    <div className="h-screen flex flex-col">
      {/* 顶部导航 */}
      <Header
        onCreateNote={handleCreateNote}
        onOpenSettings={handleOpenSettings}
      />

      <div className="flex flex-1 overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar
          currentView={currentView}
          onViewChange={setCurrentView}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          showAdminPanel={user?.role === 'admin'}
        />

        {/* 主内容区域 */}
        <main className="flex-1 overflow-hidden bg-gray-50">
          {renderMainContent()}
        </main>
      </div>

      {/* 便签编辑器 */}
      <NoteEditor
        note={selectedNote}
        isOpen={isEditorOpen}
        onClose={handleCloseEditor}
      />
    </div>
  );
};
