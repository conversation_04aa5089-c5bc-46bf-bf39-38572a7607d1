import dotenv from 'dotenv';
import { DataTypes, Sequelize } from 'sequelize';

dotenv.config();

class DatabaseConfig {
  public sequelize: Sequelize;

  constructor() {
    const databaseUrl = process.env.DATABASE_URL ||
      'postgresql://conmon_owner:<EMAIL>/conmon?sslmode=require';

    this.sequelize = new Sequelize(databaseUrl, {
      dialect: 'postgres',
      dialectOptions: {
        ssl: {
          require: true,
          rejectUnauthorized: false
        }
      },
      logging: process.env.NODE_ENV === 'development' ? console.log : false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    });

    this.defineModels();
  }

  private defineModels() {
    // 用户模型
    this.sequelize.define('User', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      email: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
      },
      password: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      avatar: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      role: {
        type: DataTypes.ENUM('user', 'admin'),
        defaultValue: 'user',
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      lastLoginAt: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      refreshToken: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    }, {
      tableName: 'users',
      timestamps: true,
    });

    // 便签模型
    this.sequelize.define('Note', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      content: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      category: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tags: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        defaultValue: [],
      },
      isPinned: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isArchived: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isDeleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      color: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      metadata: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    }, {
      tableName: 'notes',
      timestamps: true,
    });

    // 分类模型
    this.sequelize.define('Category', {
      id: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      color: {
        type: DataTypes.STRING,
        defaultValue: '#3B82F6',
      },
      icon: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      sortOrder: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
      },
      userId: {
        type: DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
      },
    }, {
      tableName: 'categories',
      timestamps: true,
    });

    // 定义关联关系将在connectDB中处理
  }

  async connectDB(): Promise<void> {
    try {
      await this.sequelize.authenticate();
      console.log('✅ PostgreSQL 连接成功');

      // 定义关联关系
      const User = this.sequelize.models.User;
      const Note = this.sequelize.models.Note;
      const Category = this.sequelize.models.Category;

      if (User && Note && Category) {
        User.hasMany(Note, { foreignKey: 'userId' });
        Note.belongsTo(User, { foreignKey: 'userId' });

        User.hasMany(Category, { foreignKey: 'userId' });
        Category.belongsTo(User, { foreignKey: 'userId' });
      }

      // 同步数据库模型
      await this.sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步完成');
    } catch (error) {
      console.error('❌ PostgreSQL 连接失败:', error);
      process.exit(1);
    }
  }

  async disconnectDB(): Promise<void> {
    try {
      await this.sequelize.close();
      console.log('✅ PostgreSQL 连接已关闭');
    } catch (error) {
      console.error('❌ 关闭 PostgreSQL 连接时出错:', error);
    }
  }

  async getConnectionState(): Promise<string> {
    try {
      await this.sequelize.authenticate();
      return 'connected';
    } catch {
      return 'disconnected';
    }
  }
}

export const config = new DatabaseConfig();
