{"name": "loft-notes", "version": "1.0.0", "description": "电子便签系统 - 现代化的便签管理应用", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd client && npm install && cd ../server && npm install"}, "keywords": ["notes", "react", "nodejs", "typescript", "shadcn"], "author": "Claude 4.0 sonnet", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "pg": "^8.16.2", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6"}}