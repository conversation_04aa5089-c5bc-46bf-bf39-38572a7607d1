import dotenv from 'dotenv';
import { Sequelize } from 'sequelize-typescript';
import { Category } from '../models/Category-pg';
import { Note } from '../models/Note-pg';
import { User } from '../models/User-pg';

dotenv.config();

class DatabaseConfig {
  public sequelize: Sequelize;

  constructor() {
    const databaseUrl = process.env.DATABASE_URL ||
      'postgresql://conmon_owner:<EMAIL>/conmon?sslmode=require';

    this.sequelize = new Sequelize(databaseUrl, {
      dialect: 'postgres',
      dialectOptions: {
        ssl: {
          require: true,
          rejectUnauthorized: false
        }
      },
      models: [User, Note, Category],
      logging: process.env.NODE_ENV === 'development' ? console.log : false,
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    });
  }

  async connectDB(): Promise<void> {
    try {
      await this.sequelize.authenticate();
      console.log('✅ PostgreSQL 连接成功');

      // 同步数据库模型
      await this.sequelize.sync({ alter: true });
      console.log('✅ 数据库模型同步完成');
    } catch (error) {
      console.error('❌ PostgreSQL 连接失败:', error);
      process.exit(1);
    }
  }

  async disconnectDB(): Promise<void> {
    try {
      await this.sequelize.close();
      console.log('✅ PostgreSQL 连接已关闭');
    } catch (error) {
      console.error('❌ 关闭 PostgreSQL 连接时出错:', error);
    }
  }

  async getConnectionState(): Promise<string> {
    try {
      await this.sequelize.authenticate();
      return 'connected';
    } catch {
      return 'disconnected';
    }
  }
}

export const config = new DatabaseConfig();
