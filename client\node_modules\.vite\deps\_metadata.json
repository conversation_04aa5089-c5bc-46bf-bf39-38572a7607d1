{"hash": "63929dec", "configHash": "21164de1", "lockfileHash": "9d39de96", "browserHash": "051590b6", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "443d936f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2d9524cf", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a07e1e2c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "de70c492", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "6de29c03", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "a2bab184", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "f9f6915b", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "66d1e2be", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "cd450ad0", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "9713fdfb", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "6c726a87", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "059424ae", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "485fb678", "needsInterop": false}, "date-fns": {"src": "../../date-fns/esm/index.js", "file": "date-fns.js", "fileHash": "ddc027d5", "needsInterop": false}, "date-fns/locale": {"src": "../../date-fns/esm/locale/index.js", "file": "date-fns_locale.js", "fileHash": "d8f82e93", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "25450969", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b1e56ecd", "needsInterop": true}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "a4ea8d23", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "a0d2efd9", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "2a9d9800", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "7c49684d", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "204e67cf", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-SJKHQ62W": {"file": "chunk-SJKHQ62W.js"}, "chunk-PRI4NJZF": {"file": "chunk-PRI4NJZF.js"}, "chunk-O4BN2DE4": {"file": "chunk-O4BN2DE4.js"}, "chunk-3BY3E6F4": {"file": "chunk-3BY3E6F4.js"}, "chunk-EWKR2EZY": {"file": "chunk-EWKR2EZY.js"}, "chunk-QIQZZCA3": {"file": "chunk-QIQZZCA3.js"}, "chunk-JFWPD3MN": {"file": "chunk-JFWPD3MN.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}