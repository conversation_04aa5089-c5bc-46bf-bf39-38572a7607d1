import jwt from 'jsonwebtoken';

export interface TokenPayload {
  id: string;
  email: string;
  role: string;
}

const JWT_SECRET = process.env.JWT_SECRET || 'default-secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'default-refresh-secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

export class JWTUtils {
  /**
   * 生成访问令牌
   */
  static generateAccessToken(payload: TokenPayload): string {
    return (jwt as any).sign(payload, JWT_SECRET, {
      expiresIn: JWT_EXPIRES_IN,
    });
  }

  /**
   * 生成刷新令牌
   */
  static generateRefreshToken(payload: TokenPayload): string {
    return (jwt as any).sign(payload, JWT_REFRESH_SECRET, {
      expiresIn: JWT_REFRESH_EXPIRES_IN,
    });
  }

  /**
   * 验证访问令牌
   */
  static verifyAccessToken(token: string): TokenPayload {
    return (jwt as any).verify(token, JWT_SECRET) as TokenPayload;
  }

  /**
   * 验证刷新令牌
   */
  static verifyRefreshToken(token: string): TokenPayload {
    return (jwt as any).verify(token, JWT_REFRESH_SECRET) as TokenPayload;
  }

  /**
   * 生成令牌对
   */
  static generateTokenPair(payload: TokenPayload) {
    return {
      accessToken: JWTUtils.generateAccessToken(payload),
      refreshToken: JWTUtils.generateRefreshToken(payload),
    };
  }

  /**
   * 从令牌中提取载荷（不验证）
   */
  static decodeToken(token: string): TokenPayload | null {
    try {
      return (jwt as any).decode(token) as TokenPayload;
    } catch (error) {
      return null;
    }
  }

  /**
   * 检查令牌是否即将过期（剩余时间少于1小时）
   */
  static isTokenExpiringSoon(token: string): boolean {
    try {
      const decoded = (jwt as any).decode(token) as any;
      if (!decoded || !decoded.exp) return true;
      
      const expirationTime = decoded.exp * 1000; // 转换为毫秒
      const currentTime = Date.now();
      const oneHour = 60 * 60 * 1000; // 1小时的毫秒数
      
      return (expirationTime - currentTime) < oneHour;
    } catch (error) {
      return true;
    }
  }
}
